defmodule Drops.Relation.Plugins.AutoRestrict do
  @moduledoc """
  Plugin that automatically generates finder functions based on database indices.

  This plugin analyzes the relation's schema indices and generates composable
  finder functions like `get_by_email/1`, `get_by_name/1`, etc.

  ## Generated Functions

  For each index in the database, this plugin generates:
  - `get_by_{field}/1` - Returns a composable relation for the field
  - `find_by_{field}/1` - Alias for `get_by_{field}/1`

  ## Examples

      # If users table has an index on email
      users = Users.get_by_email("<EMAIL>")  # Returns relation
      user = users |> Users.one()                     # Execute query

      # Composable with other operations
      active_user = Users
                    |> Users.get_by_email("<EMAIL>")
                    |> Users.restrict(active: true)
                    |> Users.one()

      # Works with composite indices too
      user = Users.get_by_email_and_role("<EMAIL>", "admin")
  """

  alias Drops.Relation.Plugins.AutoRestrict.SchemaCompiler

  use Drops.Relation.Plugin

  def on(:before_compile, _relation, %{schema: schema}) do
    functions = SchemaCompiler.visit(schema, %{})

    quote do
      (unquote_splicing(functions))
    end
  end
end
