defmodule Drops.Relation do
  @moduledoc """
  High-level API for defining database relations with automatic schema inference.

  Drops.Relation automatically introspects database tables and generates Ecto schemas,
  providing a convenient query API that delegates to Ecto.Repo functions. Relations
  support composable queries, custom query definitions, and views.

  ## Basic Usage

      defmodule MyApp.Users do
        use Drops.Relation, repo: MyApp.Repo

        schema("users", infer: true)
      end

      # Reading data
      user = MyApp.Users.get(1)
      users = MyApp.Users.all()
      active_users = MyApp.Users.all_by(active: true)

      # Writing data
      {:ok, user} = MyApp.Users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
      {:ok, user} = MyApp.Users.update(user, %{name: "<PERSON>"})

  ## Composable Queries

      # Chain operations together
      users = MyApp.Users
              |> MyApp.Users.restrict(active: true)
              |> MyApp.Users.order(:name)
              |> Enum.to_list()

  ## Custom Queries

      defmodule MyApp.Users do
        use Drops.Relation, repo: MyApp.Repo

        schema("users", infer: true)

        defquery active() do
          from(u in relation(), where: u.active == true)
        end

        defquery by_name(name) do
          from(u in relation(), where: u.name == ^name)
        end
      end

      # Use custom queries
      active_users = MyApp.Users.active() |> Enum.to_list()
      john = MyApp.Users.by_name("John") |> MyApp.Users.one()

  ## Configuration

  Relations can be configured at the application level:

      config :my_app, :drops,
        relation: [
          default_plugins: [...],
          schema_module: MyApp.CustomSchemaModule
        ]

  All query functions accept an optional `:repo` option to override the default repository.
  """

  alias Drops.Relation.Compilation

  defmacro __using__(opts) do
    __define_relation__(Macro.expand(opts, __CALLER__))
  end

  def __define_relation__(opts) do
    config =
      if opts[:source] do
        quote location: :keep do
          @config unquote(opts[:source].__config__())
          def __config__, do: @config

          @source unquote(opts[:source])
          def source, do: @source

          @view unquote(opts[:view])
          def name, do: @view
        end
      else
        quote do
          @config Application.compile_env(
                    unquote(opts)[:repo].config()[:otp_app],
                    [:drops, :relation],
                    []
                  )
          def __config__, do: @config
        end
      end

    plugins =
      Enum.map(plugins(opts), fn plugin ->
        quote do
          use unquote(plugin)
        end
      end)

    quote location: :keep do
      import Drops.Relation

      unquote(config)

      @context Compilation.Context.new(__MODULE__, @config)

      Module.register_attribute(__MODULE__, :plugins, accumulate: true)

      unquote_splicing(plugins)

      @opts unquote(opts)
      def opts, do: @opts
      def opts(name), do: Keyword.get(opts(), name)

      defmacro __using__(opts) do
        Drops.Relation.__define_relation__(
          Keyword.put(Macro.expand(opts, __CALLER__), :source, __MODULE__)
        )
      end
    end
  end

  defmacro delegate_to(fun, to: target) do
    fun = Macro.escape(fun)

    quote bind_quoted: [fun: fun, target: target] do
      {name, args} = Macro.decompose_call(fun)

      final_args =
        case args do
          [] -> [[relation: __MODULE__]]
          _ -> args ++ [[relation: __MODULE__]]
        end

      def unquote({name, [line: __ENV__.line], args}) do
        unquote(target).unquote(name)(unquote_splicing(final_args))
      end
    end
  end

  defp plugins(opts) do
    case opts[:plugins] do
      nil ->
        Drops.Relation.Config.default_plugins(opts[:repo])

      plugins when is_list(plugins) ->
        plugins
    end
  end
end
